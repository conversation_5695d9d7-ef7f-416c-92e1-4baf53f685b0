/* Standard includes. */
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <openthread/thread.h>
#include <openthread/instance.h>
#include "freertos/FreeRTOS.h"
#include "freertos/queue.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include "esp_openthread.h"
#include "esp_log.h"
#include "esp_heap_caps.h"
#include <stdbool.h>

/* Map includes. */
#include "map.h"

#define TIMESTAMP_LENGTH 32  // Buffer length for storing formatted timestamp strings.
#define MAX_ENTRIES 120       // Maximum number of entries for each data structure.
#define DEFAULT_SLEEP_TIME 5 // Default sleep time in seconds.
#define SENSOR_TYPES 8   // Number of sensor types.

/* Global Queue for Sensor Updates */
QueueHandle_t sensor_update_queue;

/* Mutex to protect sensor_entries array */
static SemaphoreHandle_t sensor_entries_mutex;

/* =========================
 * Data Structures
 * ========================= */

/* Structure for storing information about a sensor. */
typedef struct
{
    char addr[MAC_ADDR_STRLEN];
    char timestamp[TIMESTAMP_LENGTH];
    float rms[NUM_READINGS];
    int sleep_time;
    int isPowered;
    int type;
    int rssi;
    bool lowCurrent;
    uint8_t firmware_major;
    uint8_t firmware_minor;
    uint8_t firmware_patch;
    uint32_t message_id;    // 消息ID，每次传感器发送消息递增
    uint32_t retry_count;   // 重试计数，用于MQTT发布失败重试
} SensorEntry;

/* =========================
 * Global Arrays for Data Storage
 * ========================= */

/* Array to store sensor entries and its counter. */
static EXT_RAM_ATTR SensorEntry sensor_entries[MAX_ENTRIES];
static int sensor_count = 0;

/* Tag for logging */
static const char *TAG = "map";

/* =========================
 * Helper Functions and JSON Utilities
 * ========================= */

/**
 * @brief Checks for sensors with possible packet loss and triggers compensation
 * 
 * This function examines all sensors and identifies those with timestamps between 
 * 1 and 3 minutes old, which may indicate lost packets. For these sensors, it 
 * sends their MAC address to the sensor_update_queue to trigger a compensatory report.
 */
void check_and_compensate_lost_packets(void)
{
    time_t current_time;
    struct tm sensor_tm = {0};
    time_t sensor_time;
    double time_diff;
    
    // Get current time
    time(&current_time);
    
    // 检查信号量是否有效
    if (sensor_entries_mutex == NULL) {
        ESP_LOGE(TAG, "sensor_entries_mutex is NULL, skipping compensation check");
        return;
    }
    
    if (xSemaphoreTake(sensor_entries_mutex, portMAX_DELAY) == pdTRUE) {
        for (int i = 0; i < sensor_count; i++) {
            // Parse the timestamp string to time_t
            memset(&sensor_tm, 0, sizeof(struct tm));
            if (strptime(sensor_entries[i].timestamp, "%a %b  %d %H:%M:%S %Y", &sensor_tm) != NULL) {
                sensor_time = mktime(&sensor_tm);
                time_diff = difftime(current_time, sensor_time);
                
                // Check if timestamp is between 1 and 3 minutes old
                if (time_diff > 60 && time_diff < 180) {
                    // Check if the sensor is in a low current state
                    if (sensor_entries[i].lowCurrent) {
                        // Skip compensation if lowCurrent is true
                        continue;
                    }
                    // This sensor needs compensation - send MAC to queue
                    // ESP_LOGI(TAG, "Compensating for sensor %s (last update: %s, age: %.0f seconds)", 
                    //          sensor_entries[i].addr, sensor_entries[i].timestamp, time_diff);
                    
                    // Send to queue but don't block if queue is full
                    xQueueSend(sensor_update_queue, sensor_entries[i].addr, 0);
                }
            } else {
                ESP_LOGW(TAG, "Failed to parse timestamp for sensor %s: %s", 
                         sensor_entries[i].addr, sensor_entries[i].timestamp);
            }
        }
        xSemaphoreGive(sensor_entries_mutex);
    }
}

/**
 * @brief Initializes the FreeRTOS queue for sensor updates.
 */
void map_init_queue(void)
{
    sensor_update_queue = xQueueCreate(MAX_ENTRIES, MAC_ADDR_STRLEN);
    if (sensor_update_queue == NULL) {
        // TODO: Handle queue creation error
        printf("Error: Failed to create sensor update queue\n");
    }

    // Create the mutex for protecting sensor_entries
    sensor_entries_mutex = xSemaphoreCreateMutex();
    if (sensor_entries_mutex == NULL) {
         // TODO: Handle mutex creation error
        printf("Error: Failed to create sensor entries mutex\n");
    }
    
    // 删除对start_packet_loss_compensation_task的调用
}

/**
 * @brief Retrieves the version for a given MAC address.
 *
 * @param mac_addr The MAC address to look up.
 * @return The version.
 */
char* getversionByMac(char *mac_addr)
{
    char* firmware_version = heap_caps_malloc(8, MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
    if (firmware_version == NULL) {
        firmware_version = heap_caps_malloc(8, MALLOC_CAP_8BIT); // Fallback to internal RAM
    }
    if (firmware_version != NULL) {
        strcpy(firmware_version, "0.0.0"); // Default value
    }

    if (xSemaphoreTake(sensor_entries_mutex, portMAX_DELAY) == pdTRUE) {
        for (int i = 0; i < sensor_count; i++)
        {
            if (strcmp(sensor_entries[i].addr, mac_addr) == 0)
            {
                heap_caps_free(firmware_version); // Free default value
                firmware_version = heap_caps_malloc(20, MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
                if (firmware_version == NULL) {
                    firmware_version = heap_caps_malloc(20, MALLOC_CAP_8BIT); // Fallback to internal RAM
                }
                if (firmware_version != NULL)
                {
                    snprintf(firmware_version, 20, "%u.%u.%u",
                             sensor_entries[i].firmware_major,
                             sensor_entries[i].firmware_minor,
                             sensor_entries[i].firmware_patch);
                } else {
                    firmware_version = heap_caps_malloc(8, MALLOC_CAP_8BIT);
                    if (firmware_version != NULL) {
                        strcpy(firmware_version, "0.0.0"); // Revert to default on malloc failure
                    }
                }
                break;
            }
        }
        xSemaphoreGive(sensor_entries_mutex);
    }

    return firmware_version;
}

/**
 * @brief Retrieves the power status for a given MAC address.
 *
 * @param mac_addr The MAC address to look up.
 * @return The power status (1 = powered, 0 = not powered). Returns 0 if the address is not found.
 */
int getisPoweredByMac(char *mac_addr)
{
    int isPowered = 0;
    if (xSemaphoreTake(sensor_entries_mutex, portMAX_DELAY) == pdTRUE) {
        for (int i = 0; i < sensor_count; i++)
        {
            if (strcmp(sensor_entries[i].addr, mac_addr) == 0)
            {
                isPowered = sensor_entries[i].isPowered;
                break;
            }
        }
        xSemaphoreGive(sensor_entries_mutex);
    }
    return isPowered;
}

/**
 * @brief Retrieves the sleep time for a given MAC address.
 *
 * Searches for the MAC address in the sleep time entries and returns the associated sleep time.
 * If the address is not found, the default sleep time is returned.
 *
 * @param mac_addr The MAC address to look up.
 * @return The sleep time associated with the MAC address, or the default sleep time if not found.
 */
int getSleepTimeByMac(char *mac_addr)
{
    int sleepTime = DEFAULT_SLEEP_TIME;
    if (xSemaphoreTake(sensor_entries_mutex, portMAX_DELAY) == pdTRUE) {
        for (int i = 0; i < sensor_count; i++)
        {
            if (strcmp(sensor_entries[i].addr, mac_addr) == 0)
            {
                sleepTime = sensor_entries[i].sleep_time > 0 ? sensor_entries[i].sleep_time : DEFAULT_SLEEP_TIME;
                break;
            }
        }
        xSemaphoreGive(sensor_entries_mutex);
    }
    return sleepTime;
}

/**
 * @brief Retrieves the type for a given MAC address.
 *
 * @param mac_addr The MAC address to look up.
 * @return The type (0 = HYP-60, 1 = HYP-225, 2 = HYP-X).
 */
int getTypeByMac(char *mac_addr)
{
    int type = 0;
    if (xSemaphoreTake(sensor_entries_mutex, portMAX_DELAY) == pdTRUE) {
        for (int i = 0; i < sensor_count; i++)
        {
            if (strcmp(sensor_entries[i].addr, mac_addr) == 0)
            {
                type = sensor_entries[i].type >= SENSOR_TYPES ? 0 : sensor_entries[i].type;
                break;
            }
        }
        xSemaphoreGive(sensor_entries_mutex);
    }
    return type;
}

/**
 * @brief Retrieves the rssi for a given MAC address.
 *
 * @param mac_addr The MAC address to look up.
 * @return The rssi
 */
int getRssiByMac(char *mac_addr)
{
    int rssi = 0; // Default RSSI
    if (xSemaphoreTake(sensor_entries_mutex, portMAX_DELAY) == pdTRUE) {
        for (int i = 0; i < sensor_count; i++)
        {
            if (strcmp(sensor_entries[i].addr, mac_addr) == 0)
            {
                rssi = sensor_entries[i].rssi;
                break;
            }
        }
        xSemaphoreGive(sensor_entries_mutex);
    }
    return rssi;
}

/**
 * @brief Retrieves the low current status for a given MAC address.
 *
 * @param mac_addr The MAC address to look up.
 * @return The low current status (1 = low, 0 = normal). Returns 0 if the address is not found.
 */
bool getLowCurrentByMac(char *mac_addr)
{
    bool lowCurrent = false;
    if (xSemaphoreTake(sensor_entries_mutex, portMAX_DELAY) == pdTRUE) {
        for (int i = 0; i < sensor_count; i++)
        {
            if (strcmp(sensor_entries[i].addr, mac_addr) == 0)
            {
                lowCurrent = sensor_entries[i].lowCurrent;
                break;
            }
        }
        xSemaphoreGive(sensor_entries_mutex);
    }
    return lowCurrent;
}

/**
 * @brief Retrieves the message_id for a given MAC address.
 *
 * @param mac_addr The MAC address to look up.
 * @return The message_id
 */
uint32_t getMessageIdByMac(char *mac_addr)
{
    uint32_t message_id = 0; // Default message_id
    if (xSemaphoreTake(sensor_entries_mutex, portMAX_DELAY) == pdTRUE) {
        for (int i = 0; i < sensor_count; i++)
        {
            if (strcmp(sensor_entries[i].addr, mac_addr) == 0)
            {
                message_id = sensor_entries[i].message_id;
                break;
            }
        }
        xSemaphoreGive(sensor_entries_mutex);
    }
    return message_id;
}

/**
 * @brief Adds or updates the message_id for a given MAC address.
 *
 * If the MAC address exists in the entries, the message_id is updated.
 * If the address does not exist and there is space, a new entry is added.
 *
 * @param mac_addr The MAC address of the device.
 * @param message_id The message_id value.
 */
void addOrUpdateMessageId(char *mac_addr, uint32_t message_id)
{
     if (xSemaphoreTake(sensor_entries_mutex, portMAX_DELAY) == pdTRUE) {
        for (int i = 0; i < sensor_count; i++)
        {
            if (strcmp(sensor_entries[i].addr, mac_addr) == 0)
            {
                sensor_entries[i].message_id = message_id;
                xSemaphoreGive(sensor_entries_mutex);
                return;
            }
        }
        if (sensor_count < MAX_ENTRIES)
        {
            strcpy(sensor_entries[sensor_count].addr, mac_addr);
            sensor_entries[sensor_count].message_id = message_id;
            sensor_count++;
        }
         xSemaphoreGive(sensor_entries_mutex);
    }
}

/**
 * @brief Adds or updates the version for a given address.
 *
 * @param addr_only The device address.
 * @param version
 */
void addOrUpdateFirmwareVersion(char *addr_only, uint8_t major, uint8_t minor, uint8_t patch)
{
    if (xSemaphoreTake(sensor_entries_mutex, portMAX_DELAY) == pdTRUE) {
        for (int i = 0; i < sensor_count; i++)
        {
            if (strcmp(sensor_entries[i].addr, addr_only) == 0)
            {
                sensor_entries[i].firmware_major = major;
                sensor_entries[i].firmware_minor = minor;
                sensor_entries[i].firmware_patch = patch;
                xSemaphoreGive(sensor_entries_mutex);
                return;
            }
        }

        if (sensor_count < MAX_ENTRIES)
        {
            strcpy(sensor_entries[sensor_count].addr, addr_only);
            sensor_entries[sensor_count].firmware_major = major;
            sensor_entries[sensor_count].firmware_minor = minor;
            sensor_entries[sensor_count].firmware_patch = patch;
            sensor_count++;
        }
        xSemaphoreGive(sensor_entries_mutex);
    }
}


/**
 * @brief Adds or updates current RMS values for a given address.
 *
 * If the address exists in the current entries, the RMS values are updated.
 * If the address does not exist and there is space, a new entry is added.
 *
 * @param addr_only The device address.
 * @param rms Pointer to an array of RMS values.
 */
void addOrUpdateCurrent(char *addr_only, float *rms)
{
    if (xSemaphoreTake(sensor_entries_mutex, portMAX_DELAY) == pdTRUE) {
        for (int i = 0; i < sensor_count; i++)
        {
            if (strcmp(sensor_entries[i].addr, addr_only) == 0)
            {
                memcpy(sensor_entries[i].rms, rms, 12 * sizeof(float));
                xSemaphoreGive(sensor_entries_mutex);
                return;
            }
        }
        if (sensor_count < MAX_ENTRIES)
        {
            strcpy(sensor_entries[sensor_count].addr, addr_only);
            memcpy(sensor_entries[sensor_count].rms, rms, 12 * sizeof(float));
            sensor_count++;
        }
        xSemaphoreGive(sensor_entries_mutex);
    }
}

/**
 * @brief Adds or updates the timestamp for a given address.
 *
 * If the address exists in the timestamp entries, the timestamp is updated.
 * If the address does not exist and there is space, a new entry is added.
 *
 * @param addr_only The device address.
 * @param timestamp The timestamp string to store.
 */
void addOrUpdateTimestamp(char *addr_only, char *timestamp)
{
     if (xSemaphoreTake(sensor_entries_mutex, portMAX_DELAY) == pdTRUE) {
        for (int i = 0; i < sensor_count; i++)
        {
            if (strcmp(sensor_entries[i].addr, addr_only) == 0)
            {
                strcpy(sensor_entries[i].timestamp, timestamp);
                xSemaphoreGive(sensor_entries_mutex);
                return;
            }
        }
        if (sensor_count < MAX_ENTRIES)
        {
            strcpy(sensor_entries[sensor_count].addr, addr_only);
            strcpy(sensor_entries[sensor_count].timestamp, timestamp);
            sensor_count++;
        }
         xSemaphoreGive(sensor_entries_mutex);
    }
}

/**
 * @brief Adds or updates the sleep time for a given MAC address.
 *
 * If the MAC address exists in the sleep time entries, the sleep time is updated.
 * If the address does not exist and there is space, a new entry is added.
 *
 * @param mac_addr The MAC address of the device.
 * @param sleep_time The sleep time in seconds.
 */
void addOrUpdateSleepTime(char *mac_addr, int sleep_time)
{
     if (xSemaphoreTake(sensor_entries_mutex, portMAX_DELAY) == pdTRUE) {
        for (int i = 0; i < sensor_count; i++)
        {
            if (strcmp(sensor_entries[i].addr, mac_addr) == 0)
            {
                sensor_entries[i].sleep_time = sleep_time;
                xSemaphoreGive(sensor_entries_mutex);
                return;
            }
        }
        if (sensor_count < MAX_ENTRIES)
        {
            strcpy(sensor_entries[sensor_count].addr, mac_addr);
            sensor_entries[sensor_count].sleep_time = sleep_time;
            sensor_count++;
        }
         xSemaphoreGive(sensor_entries_mutex);
    }
}

/**
 * @brief Adds or updates the power status for a given MAC address.
 *
 * If the MAC address exists in the power status entries, the status is updated.
 * If the address does not exist and there is space, a new entry is added.
 *
 * @param mac_addr The MAC address of the device.
 * @param isPowered The power status (1 = powered, 0 = not powered).
 */
void addOrUpdateisPowered(char *mac_addr, int isPowered)
{
     if (xSemaphoreTake(sensor_entries_mutex, portMAX_DELAY) == pdTRUE) {
        for (int i = 0; i < sensor_count; i++)
        {
            if (strcmp(sensor_entries[i].addr, mac_addr) == 0)
            {
                sensor_entries[i].isPowered = isPowered;
                xSemaphoreGive(sensor_entries_mutex);
                return;
            }
        }
        if (sensor_count < MAX_ENTRIES)
        {
            strcpy(sensor_entries[sensor_count].addr, mac_addr);
            sensor_entries[sensor_count].isPowered = isPowered;
            sensor_count++;
        }
         xSemaphoreGive(sensor_entries_mutex);
    }
}

/**
 * @brief Adds or updates the type for a given MAC address.
 *
 * If the MAC address exists in the type entries, the status is updated.
 * If the address does not exist and there is space, a new entry is added.
 *
 * @param mac_addr The MAC address of the device.
 * @param type The type (0 = HYP-60, 1 = HYP-225, 2 = HYP-X).
 */
void addOrUpdateType(char *mac_addr, int type)
{
     if (xSemaphoreTake(sensor_entries_mutex, portMAX_DELAY) == pdTRUE) {
        for (int i = 0; i < sensor_count; i++)
        {
            if (strcmp(sensor_entries[i].addr, mac_addr) == 0)
            {
                sensor_entries[i].type = type;
                xSemaphoreGive(sensor_entries_mutex);
                return;
            }
        }
        if (sensor_count < MAX_ENTRIES)
        {
            strcpy(sensor_entries[sensor_count].addr, mac_addr);
            sensor_entries[sensor_count].type = type;
            sensor_count++;
        }
         xSemaphoreGive(sensor_entries_mutex);
    }
}

/**
 * @brief Adds or updates the rssi for a given MAC address.
 *
 * If the MAC address exists in the power status entries, the status is updated.
 * If the address does not exist and there is space, a new entry is added.
 *
 * @param mac_addr The MAC address of the device.
 * @param rssi The rssi.
 */
void addOrUpdateRssi(char *mac_addr, int rssi)
{
     if (xSemaphoreTake(sensor_entries_mutex, portMAX_DELAY) == pdTRUE) {
        for (int i = 0; i < sensor_count; i++)
        {
            if (strcmp(sensor_entries[i].addr, mac_addr) == 0)
            {
                sensor_entries[i].rssi = rssi;
                xSemaphoreGive(sensor_entries_mutex);
                return;
            }
        }
        if (sensor_count < MAX_ENTRIES)
        {
            strcpy(sensor_entries[sensor_count].addr, mac_addr);
            sensor_entries[sensor_count].rssi = rssi;
            sensor_count++;
        }
         xSemaphoreGive(sensor_entries_mutex);
    }
}

/**
 * @brief Adds or updates the low current status for a given MAC address.
 *
 * If the MAC address exists in the power status entries, the status is updated.
 * If the address does not exist and there is space, a new entry is added.
 *
 * @param mac_addr The MAC address of the device.
 * @param lowCurrent The low current status (1 for low, 0 for normal).
 */
void addOrUpdateLowCurrent(char *mac_addr, bool lowCurrent)
{
     if (xSemaphoreTake(sensor_entries_mutex, portMAX_DELAY) == pdTRUE) {
        for (int i = 0; i < sensor_count; i++)
        {
            if (strcmp(sensor_entries[i].addr, mac_addr) == 0)
            {
                sensor_entries[i].lowCurrent = lowCurrent;
                xSemaphoreGive(sensor_entries_mutex);
                return;
            }
        }
        if (sensor_count < MAX_ENTRIES)
        {
            strcpy(sensor_entries[sensor_count].addr, mac_addr);
            sensor_entries[sensor_count].lowCurrent = lowCurrent;
            sensor_count++;
        }
         xSemaphoreGive(sensor_entries_mutex);
    }
}

/**
 * @brief Prints the current RMS readings for all stored entries.
 *
 * Displays the device address and its corresponding RMS values.
 */
void printCurrentVector()
{
    if (sensor_entries_mutex == NULL) {
        printf("Mutex not initialized, cannot print current vector\n");
        return;
    }

    if (xSemaphoreTake(sensor_entries_mutex, portMAX_DELAY) == pdTRUE) {
        for (int i = 0; i < sensor_count; i++)
        {
            printf("DeviceName: %s\nCurrent Values: ", sensor_entries[i].addr);
            for (int j = 0; j < 12; j++)
            {
                printf("%.3f", sensor_entries[i].rms[j]);
                if (j < 11)
                {
                    printf(", ");
                }
            }
            printf("\n");
        }
        xSemaphoreGive(sensor_entries_mutex);
    }
}

/**
 * @brief Prints the sleep times for all stored entries.
 *
 * Displays each device address and its corresponding sleep time.
 */
void printSleepTimeVector()
{
    if (sensor_entries_mutex == NULL) {
        printf("Mutex not initialized, cannot print sleep times\n");
        return;
    }

    if (xSemaphoreTake(sensor_entries_mutex, portMAX_DELAY) == pdTRUE) {
        printf("\n==== Sleep Times ====\n");
        for (int i = 0; i < sensor_count; i++)
        {
            printf("%s: %d seconds\n", sensor_entries[i].addr, sensor_entries[i].sleep_time);
        }
        printf("=============================\n");
        xSemaphoreGive(sensor_entries_mutex);
    }
}

/**
 * @brief Prints the power status for all stored entries.
 *
 * Displays each device address and its power status (1 = powered, 0 = not powered).
 */
void printisPoweredVector()
{
    if (sensor_entries_mutex == NULL) {
        printf("Mutex not initialized, cannot print isPowered vector\n");
        return;
    }

    if (xSemaphoreTake(sensor_entries_mutex, portMAX_DELAY) == pdTRUE) {
        printf("\n==== isPowered ====\n");
        for (int i = 0; i < sensor_count; i++)
        {
            printf("%s: %d\n", sensor_entries[i].addr, sensor_entries[i].isPowered);
        }
        printf("=============================\n");
        xSemaphoreGive(sensor_entries_mutex);
    }
}

/**
 * @brief Displays the timestamps for all stored entries.
 *
 * Shows each device address and its corresponding timestamp.
 */
void displayTimestamps()
{
    if (sensor_entries_mutex == NULL) {
        printf("Mutex not initialized, cannot display timestamps\n");
        return;
    }

    if (xSemaphoreTake(sensor_entries_mutex, portMAX_DELAY) == pdTRUE) {
        printf("\n==== Timestamps ====\n");
        for (int i = 0; i < sensor_count; i++)
        {
            printf("%s: %s\n", sensor_entries[i].addr, sensor_entries[i].timestamp);
        }
        printf("=============================\n");
        xSemaphoreGive(sensor_entries_mutex);
    }
}

/**
 * @brief Prints the number of direct child nodes connected to the current OpenThread device.
 *
 * This function iterates through the neighbor table of the OpenThread instance and counts 
 * the number of direct child nodes associated with the current device. It then prints 
 * the total count to the console.
 *
 * @param[in] aInstance  A pointer to the OpenThread instance.
 *
 * @note This function assumes the current device is a Router or Leader. If the device
 *       is in the Child role, it will not have direct child nodes.
 *
 * Example output:
 * ```
 * Number of connected child nodes: 3
 * ```
 */
uint16_t getChildCount()
{
    otInstance *aInstance = esp_openthread_get_instance();
    otNeighborInfoIterator iterator = OT_NEIGHBOR_INFO_ITERATOR_INIT;
    otNeighborInfo neighborInfo;
    uint16_t childCount = 0;

    // Traverse the neighbor table
    if (aInstance != NULL) {
        while (otThreadGetNextNeighborInfo(aInstance, &iterator, &neighborInfo) == OT_ERROR_NONE)
        {
            if (neighborInfo.mIsChild) // Determine whether it is a child node
            {
                childCount++;
            }
        }
    }
    else
    {
        printf("Error: OpenThread instance is NULL\n");
    }

    return childCount;
}

/**
 * @brief Converts the stored data for a given address to a JSON string and optionally removes data from storage.
 *
 * @param addr_only The device address.
 * @return A dynamically allocated JSON string representing the data.
 */
char *arrayToJson(char *addr_only)
{
    /* ---- 0. 预先声明临时变量（与B相同风格） ---- */
    char     timestamp[TIMESTAMP_LENGTH] = {0};
    float    rms[NUM_READINGS]           = {0};
    int      isPowered = 0, sleepTime = DEFAULT_SLEEP_TIME;
    int      type = 0, rssi = 0;
    uint8_t  fw_major = 0, fw_minor = 0, fw_patch = 0;
    bool     found = false, lowCurrent = false;
    int      found_index = -1;
    uint32_t message_id = 0;

    /* ---- 1. 复制数据：加锁保护（与B一致） ---- */
    if (xSemaphoreTake(sensor_entries_mutex, portMAX_DELAY) == pdTRUE)
    {
        for (int i = 0; i < sensor_count; i++)
        {
            if (strcmp(sensor_entries[i].addr, addr_only) == 0)
            {
                memcpy(timestamp,  sensor_entries[i].timestamp, TIMESTAMP_LENGTH);
                memcpy(rms,        sensor_entries[i].rms,       sizeof(rms));
                isPowered  = sensor_entries[i].isPowered;
                sleepTime  = sensor_entries[i].sleep_time > 0 ? sensor_entries[i].sleep_time : DEFAULT_SLEEP_TIME;
                type       = sensor_entries[i].type;
                rssi       = sensor_entries[i].rssi;
                lowCurrent = sensor_entries[i].lowCurrent;
                fw_major   = sensor_entries[i].firmware_major;
                fw_minor   = sensor_entries[i].firmware_minor;
                fw_patch   = sensor_entries[i].firmware_patch;
                message_id = sensor_entries[i].message_id;
                found      = true;
                found_index = i;
                break;
            }
        }

        if (found && found_index >= 0 && RELEASE_SENSOR_DATA_AFTER_JSON) {
            if (found_index < sensor_count - 1) {
                memmove(&sensor_entries[found_index],
                        &sensor_entries[found_index + 1],
                        (sensor_count - found_index - 1) * sizeof(SensorEntry));
            }
            sensor_count--;
            memset(&sensor_entries[sensor_count], 0, sizeof(SensorEntry));
        }

        xSemaphoreGive(sensor_entries_mutex);
    }

    if (!found) {
        return NULL;
    }

    /* ---- 2. 脱离临界区后再做耗时格式化（与B一致的做法） ---- */

    char version[20];
    snprintf(version, sizeof(version), "%u.%u.%u", fw_major, fw_minor, fw_patch);

    /* 与B一致：预估一个固定上限的缓冲区，而非两遍精确计长 */
    const int MAX_READING_LENGTH = 64;
    const int BASE_LENGTH = 220;
    const size_t bufferSize = BASE_LENGTH + (MAX_READING_LENGTH * NUM_READINGS);

    char *buffer = (char *)heap_caps_malloc(bufferSize, MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
    if (!buffer) {
        // Fallback to internal RAM if PSRAM allocation fails
        buffer = (char *)heap_caps_malloc(bufferSize, MALLOC_CAP_8BIT);
    }
    if (!buffer) return NULL;

    /* 时间解析与“1–3分钟替换为当前时间”的规则（与B一致） */
    struct tm tm = {0};
    strptime(timestamp, "%a %b  %d %H:%M:%S %Y", &tm);
    time_t base_time = mktime(&tm);

    time_t current_time;
    time(&current_time);
    double time_diff = difftime(current_time, base_time);
    if (time_diff > 60 && time_diff < 180) {
        base_time = current_time;
    }

    /* 顶层时间串 */
    char timeStr[32];
    strftime(timeStr, sizeof(timeStr), "%Y-%m-%dT%H:%M:%S", localtime(&base_time));

    /* 传感器类型/版本（与B一致） */
    const char *sensorTypes[]    = {"HYP-60","HYP-225","HYP-X","HYP-60","HYP-225","HYP-225","HYP-60","HYP-60"};
    const int   sensorRevision[] = {1,1,1,2,2,3,3,4};

    /* 与B一致：单次累加写入，不做二次分配；保留当前JSON格式（键名不变、readings为对象数组） */
    int offset = 0;
    char message_id_str[50] = "";
    if (message_id > 0) {
        snprintf(message_id_str, sizeof(message_id_str), "  \"messageId\": %lu,\n", (unsigned long)message_id);
    }

    offset = snprintf(buffer, bufferSize,
                      "{\n"
                      "  \"type\": \"%s\",\n"
                      "  \"hwRevision\": \"%d\",\n"
                      "  \"version\": \"%s\",\n"
                      "  \"timestamp\": \"%sZ\",\n"
                      "  \"rssi\": %d,\n"
                      "  \"lowCurrent\": %s,\n"
                      "  \"intervalSeconds\": %d,\n"
                      "  \"unit\": \"A\",\n"
                      "%s"
                      "  \"readings\": [",
                      sensorTypes[type >= SENSOR_TYPES ? 0 : type],
                      sensorRevision[type >= SENSOR_TYPES ? 0 : type],
                      version,
                      timeStr,
                      rssi,
                      lowCurrent ? "true" : "false",
                      isPowered ? DEFAULT_SLEEP_TIME : sleepTime,
                      message_id_str);

    if (isPowered)
    {
        /* 与B一致：上电时发送1个均值，这里保持当前JSON格式（带timestamp+value对象） */
        float sum = 0.0f;
        for (int i = 0; i < NUM_READINGS; i++) sum += rms[i];
        float avg = sum / NUM_READINGS;

        offset += snprintf(buffer + offset, bufferSize - offset,
                           "\n    {\"timestamp\": \"%sZ\", \"value\": %.3f}\n  ]",
                           timeStr, avg);
    }
    else
    {
        /* 与B一致：发全部 NUM_READINGS 个样本；但保留当前JSON格式为对象数组（每点带时间戳） */
        for (int i = 0; i < NUM_READINGS; i++)
        {
            time_t reading_time = base_time - ((NUM_READINGS - 1 - i) * sleepTime);
            strftime(timeStr, sizeof(timeStr), "%Y-%m-%dT%H:%M:%S", localtime(&reading_time));

            offset += snprintf(buffer + offset, bufferSize - offset,
                               "%s\n    {\"timestamp\": \"%sZ\", \"value\": %.3f}",
                               i == 0 ? "" : ",", timeStr, rms[i]);
        }
        offset += snprintf(buffer + offset, bufferSize - offset, "\n  ]");
    }

    snprintf(buffer + offset, bufferSize - offset, "\n}");
    return buffer;
}

/**
 * @brief 获取指定MAC地址的重试计数
 *
 * @param mac_addr MAC地址
 * @return uint32_t 重试计数，如果未找到返回0
 */
uint32_t getRetryCountByMac(char *mac_addr)
{
    if (xSemaphoreTake(sensor_entries_mutex, portMAX_DELAY) == pdTRUE) {
        for (int i = 0; i < sensor_count; i++)
        {
            if (strcmp(sensor_entries[i].addr, mac_addr) == 0)
            {
                uint32_t count = sensor_entries[i].retry_count;
                xSemaphoreGive(sensor_entries_mutex);
                return count;
            }
        }
        xSemaphoreGive(sensor_entries_mutex);
    }
    return 0; // 未找到或获取锁失败，返回0
}

/**
 * @brief 递增指定MAC地址的重试计数
 *
 * @param mac_addr MAC地址
 */
void incrementRetryCountByMac(char *mac_addr)
{
    if (xSemaphoreTake(sensor_entries_mutex, portMAX_DELAY) == pdTRUE) {
        for (int i = 0; i < sensor_count; i++)
        {
            if (strcmp(sensor_entries[i].addr, mac_addr) == 0)
            {
                sensor_entries[i].retry_count++;
                xSemaphoreGive(sensor_entries_mutex);
                return;
            }
        }
        xSemaphoreGive(sensor_entries_mutex);
    }
}

/**
 * @brief 重置指定MAC地址的重试计数为0
 *
 * @param mac_addr MAC地址
 */
void resetRetryCountByMac(char *mac_addr)
{
    if (xSemaphoreTake(sensor_entries_mutex, portMAX_DELAY) == pdTRUE) {
        for (int i = 0; i < sensor_count; i++)
        {
            if (strcmp(sensor_entries[i].addr, mac_addr) == 0)
            {
                sensor_entries[i].retry_count = 0;
                xSemaphoreGive(sensor_entries_mutex);
                return;
            }
        }
        xSemaphoreGive(sensor_entries_mutex);
    }
}
